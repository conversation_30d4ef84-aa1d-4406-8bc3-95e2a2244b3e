1<?xml version="1.0" encoding="utf-8"?>
2<!-- GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN -->
3<manifest xmlns:android="http://schemas.android.com/apk/res/android"
4    package="com.cy.exercisetv"
5    android:installLocation="preferExternal"
6    android:versionCode="31"
7    android:versionName="1.3.2" >
8
9    <uses-sdk
10        android:minSdkVersion="21"
10-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
11        android:targetSdkVersion="30" />
11-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
12
13    <supports-screens
13-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:3-163
14        android:anyDensity="true"
14-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:135-160
15        android:largeScreens="true"
15-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:78-105
16        android:normalScreens="true"
16-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:49-77
17        android:smallScreens="true"
17-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:21-48
18        android:xlargeScreens="true" />
18-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:106-134
19
20    <uses-feature android:glEsVersion="0x00030000" />
20-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-54
20-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:19-51
21    <uses-feature
21-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-14:36
22        android:name="android.hardware.vulkan.version"
22-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-55
23        android:required="false" />
23-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-33
24    <uses-feature
24-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-17:36
25        android:name="android.hardware.camera"
25-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-47
26        android:required="false" />
26-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:9-33
27    <uses-feature
27-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:5-20:36
28        android:name="android.hardware.camera.autofocus"
28-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-57
29        android:required="false" />
29-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-33
30    <uses-feature
30-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:5-23:36
31        android:name="android.hardware.camera.front"
31-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:9-53
32        android:required="false" />
32-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:9-33
33    <uses-feature
33-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:5-26:36
34        android:name="android.hardware.touchscreen"
34-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:9-52
35        android:required="false" />
35-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33
36    <uses-feature
36-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:5-29:36
37        android:name="android.hardware.touchscreen.multitouch"
37-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-63
38        android:required="false" />
38-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-33
39    <uses-feature
39-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:5-32:36
40        android:name="android.hardware.touchscreen.multitouch.distinct"
40-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:9-72
41        android:required="false" />
41-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:32:9-33
42
43    <uses-permission android:name="android.permission.INTERNET" />
43-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:5-67
43-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:22-64
44    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
44-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:5-81
44-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:22-78
45    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
45-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:5-83
45-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:22-80
46    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
46-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:5-79
46-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:22-76
47    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
47-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:5-76
47-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:22-73
48    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
48-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:5-75
48-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:22-72
49    <uses-permission android:name="android.permission.CAMERA" />
49-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:5-65
49-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:22-62
50    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
50-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:5-76
50-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:22-73
51    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
51-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:5-79
51-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:22-76
52    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
52-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:5-81
52-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:22-78
53    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
53-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:5-79
53-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:22-76
54    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
54-->[com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:5-79
54-->[com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:22-76
55
56    <application
56-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:5:3-83
57        android:name="com.unity3d.player.App"
57-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:18-55
58        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
58-->[androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:24:18-86
59        android:debuggable="true"
60        android:icon="@mipmap/app_icon"
60-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:5:49-80
61        android:label="@string/app_name"
61-->E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:5:16-48
62        android:testOnly="true" >
63        <activity
63-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:9-77:20
64            android:name="com.unity3d.player.UnityPlayerActivity"
64-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:13-66
65            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
65-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:13-194
66            android:exported="true"
66-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:13-36
67            android:hardwareAccelerated="false"
67-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:13-48
68            android:launchMode="singleTask"
68-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:13-44
69            android:resizeableActivity="false"
69-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:13-47
70            android:screenOrientation="landscape"
70-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:13-50
71            android:theme="@style/UnityThemeSelector" >
71-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:13-54
72
73            <!-- <intent-filter android:priority="1000"> -->
74            <!-- <action android:name="android.intent.action.MAIN" /> -->
75
76
77            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
78            <!-- <category android:name="android.intent.category.HOME" /> -->
79            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
80            <!-- </intent-filter> -->
81            <intent-filter>
81-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:65:13-69:29
82                <action android:name="android.intent.action.MAIN" />
82-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:17-69
82-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:25-66
83
84                <category android:name="android.intent.category.LAUNCHER" />
84-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:17-77
84-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:27-74
85            </intent-filter>
86
87            <meta-data
87-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:13-73:40
88                android:name="unityplayer.UnityActivity"
88-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:17-57
89                android:value="true" />
89-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:17-37
90            <meta-data
90-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:74:13-76:40
91                android:name="android.notch_support"
91-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:75:17-53
92                android:value="true" />
92-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:17-37
93        </activity>
94
95        <meta-data
95-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:9-81:33
96            android:name="unity.splash-mode"
96-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:13-45
97            android:value="0" />
97-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:13-30
98        <meta-data
98-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:9-84:36
99            android:name="unity.splash-enable"
99-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:13-47
100            android:value="True" />
100-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:84:13-33
101        <meta-data
101-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:85:9-87:37
102            android:name="unity.allow-resizable-window"
102-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:13-56
103            android:value="False" />
103-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:13-34
104        <meta-data
104-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:9-90:50
105            android:name="notch.config"
105-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:13-40
106            android:value="portrait|landscape" />
106-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:13-47
107        <meta-data
107-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:9-93:64
108            android:name="TEST_DEVICE_ID"
108-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:13-42
109            android:value="2A27406B9CA8A5472970D77E263F014F" />
109-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:13-61
110        <meta-data
110-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:9-96:68
111            android:name="unity.build-id"
111-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:13-42
112            android:value="3e5cc2c2-84b1-41e0-ae5f-809a2a5eda1f" />
112-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:13-65
113        <meta-data
113-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:9-99:56
114            android:name="UMENG_APPKEY"
114-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:13-40
115            android:value="686a18c779267e0210a1cb03" />
115-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:13-53
116        <meta-data
116-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:100:9-102:38
117            android:name="UMENG_CHANNEL"
117-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:13-41
118            android:value="uemeng" />
118-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:13-35
119
120        <provider
120-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:9-112:20
121            android:name="androidx.core.content.FileProvider"
121-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:13-62
122            android:authorities="com.cy.exercisetv.fileprovider"
122-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:13-64
123            android:exported="false"
123-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:13-37
124            android:grantUriPermissions="true" >
124-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:13-47
125            <meta-data
125-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:109:13-111:54
126                android:name="android.support.FILE_PROVIDER_PATHS"
126-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:110:17-67
127                android:resource="@xml/file_paths" />
127-->[:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:111:17-51
128        </provider>
129
130        <service
130-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:10:9-14:41
131            android:name="com.efs.sdk.memleaksdk.monitor.UMonitorService"
131-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:11:13-74
132            android:enabled="true"
132-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:12:13-35
133            android:exported="false"
133-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:13:13-37
134            android:process=":u_heap" />
134-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:14:13-38
135    </application>
136
137</manifest>
