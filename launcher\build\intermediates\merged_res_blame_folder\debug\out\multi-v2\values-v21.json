{"logs": [{"outputFile": "E:\\huoli-rk3566\\hl071302\\launcher\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-v21\\values-v21.xml", "map": [{"source": "E:\\huoli-rk3566\\hl071302\\launcher\\src\\main\\res\\values-v21\\styles.xml", "from": {"startLines": "2", "startColumns": "0", "startOffsets": "53", "endLines": "3", "endColumns": "8", "endOffsets": "153"}, "to": {"startLines": "6", "startColumns": "4", "startOffsets": "354", "endLines": "7", "endColumns": "8", "endOffsets": "453"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,354,470,596,722,850,1022", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,465,591,717,845,1017,1355"}, "to": {"startLines": "2,3,4,5,8,9,10,11,12,15", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,458,574,700,826,954,1126", "endLines": "2,3,4,5,8,9,10,11,14,19", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,569,695,821,949,1121,1459"}}]}]}