<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs"><file name="arm64-v8a/gamesdk_classes_dex.o" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\arm64-v8a\gamesdk_classes_dex.o"/><file name="arm64-v8a/libil2cpp.so" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\arm64-v8a\libil2cpp.so"/><file name="arm64-v8a/libmain.so" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\arm64-v8a\libmain.so"/><file name="arm64-v8a/libunity.so" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\arm64-v8a\libunity.so"/><file name="arm64-v8a/lib_burst_generated.so" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\arm64-v8a\lib_burst_generated.so"/><file name="arm64-v8a/lib_burst_generated.txt" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\arm64-v8a\lib_burst_generated.txt"/><file name="armeabi-v7a/gamesdk_classes_dex.o" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\armeabi-v7a\gamesdk_classes_dex.o"/><file name="armeabi-v7a/libil2cpp.so" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\armeabi-v7a\libil2cpp.so"/><file name="armeabi-v7a/libmain.so" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\armeabi-v7a\libmain.so"/><file name="armeabi-v7a/libunity.so" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\armeabi-v7a\libunity.so"/><file name="armeabi-v7a/lib_burst_generated.so" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\armeabi-v7a\lib_burst_generated.so"/><file name="armeabi-v7a/lib_burst_generated.txt" path="E:\huoli-rk3566\hl071302\unityLibrary\src\main\jniLibs\armeabi-v7a\lib_burst_generated.txt"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\huoli-rk3566\hl071302\unityLibrary\src\debug\jniLibs"/></dataSet></merger>