-- Merging decision tree log ---
manifest
ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:1-6:12
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:1-6:12
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:1-6:12
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:1-6:12
MERGED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:3:1-115:12
MERGED from [androidx.multidex:multidex:2.0.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\01d61b4dcd5f3b8488187f4275656988\multidex-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [:CYMobileJNI-release:] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\83ec5a2222a27734caf359c4af10dc74\jetified-CYMobileJNI-release\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:17:1-26:12
MERGED from [com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:2:1-13:12
MERGED from [com.umeng.umsdk:asms:1.8.7.2] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\13ac9d075db7c755c8063c8039a2adb2\jetified-asms-1.8.7.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:2:1-17:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.0.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\86c84a825357d05c2f724246490a8df6\lifecycle-runtime-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\ee32a41b299ebcf4e78d691344f52243\versionedparcelable-1.1.0\AndroidManifest.xml:17:1-27:12
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:1-6:12
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:1-6:12
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:1-6:12
	package
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:70-111
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:1-6:12
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:112-158
	android:versionCode
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:1-6:12
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:11-69
	android:installLocation
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:3:159-199
supports-screens
ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:3-163
	android:largeScreens
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:78-105
	android:smallScreens
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:21-48
	android:normalScreens
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:49-77
	android:xlargeScreens
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:106-134
	android:anyDensity
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:4:135-160
application
ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:5:3-83
MERGED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:5-113:19
MERGED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:5-113:19
MERGED from [androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:24:5-89
MERGED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:9:5-15:19
MERGED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:9:5-15:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\ee32a41b299ebcf4e78d691344f52243\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\ee32a41b299ebcf4e78d691344f52243\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:24:18-86
	android:label
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:5:16-48
	android:icon
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml:5:49-80
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:18-55
uses-sdk
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
MERGED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-9:41
MERGED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.multidex:multidex:2.0.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\01d61b4dcd5f3b8488187f4275656988\multidex-2.0.0\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\01d61b4dcd5f3b8488187f4275656988\multidex-2.0.0\AndroidManifest.xml:20:5-43
MERGED from [:CYMobileJNI-release:] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\83ec5a2222a27734caf359c4af10dc74\jetified-CYMobileJNI-release\AndroidManifest.xml:5:5-7:41
MERGED from [:CYMobileJNI-release:] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\83ec5a2222a27734caf359c4af10dc74\jetified-CYMobileJNI-release\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:7:5-9:41
MERGED from [com.umeng.umsdk:asms:1.8.7.2] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\13ac9d075db7c755c8063c8039a2adb2\jetified-asms-1.8.7.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.umeng.umsdk:asms:1.8.7.2] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\13ac9d075db7c755c8063c8039a2adb2\jetified-asms-1.8.7.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.0.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\86c84a825357d05c2f724246490a8df6\lifecycle-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.0.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\86c84a825357d05c2f724246490a8df6\lifecycle-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\ee32a41b299ebcf4e78d691344f52243\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\ee32a41b299ebcf4e78d691344f52243\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
		ADDED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
		INJECTED from E:\huoli-rk3566\hl071302\launcher\src\main\AndroidManifest.xml
uses-feature#0x00030000
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-54
	android:glEsVersion
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:19-51
uses-feature#android.hardware.vulkan.version
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-14:36
	android:required
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-33
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-55
uses-feature#android.hardware.camera
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-17:36
	android:required
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:9-33
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:5-20:36
	android:required
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-33
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-57
uses-feature#android.hardware.camera.front
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:5-23:36
	android:required
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:9-33
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:9-53
uses-feature#android.hardware.touchscreen
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:5-26:36
	android:required
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:9-52
uses-feature#android.hardware.touchscreen.multitouch
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:5-29:36
	android:required
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-33
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-63
uses-feature#android.hardware.touchscreen.multitouch.distinct
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:5-32:36
	android:required
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:32:9-33
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:9-72
uses-permission#android.permission.INTERNET
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:5-67
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:5-81
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:22-78
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:5-83
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:22-80
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:5-79
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:5-76
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:22-73
uses-permission#android.permission.READ_PHONE_STATE
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:5-75
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:22-72
uses-permission#android.permission.CAMERA
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:5-65
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:22-62
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:5-76
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:22-73
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:5-79
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:5-81
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:22-78
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:5-79
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:22-76
activity#com.unity3d.player.UnityPlayerActivity
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:9-77:20
	android:screenOrientation
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:13-50
	android:launchMode
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:13-44
	android:hardwareAccelerated
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:13-48
	android:exported
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:13-36
	android:resizeableActivity
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:13-47
	android:configChanges
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:13-194
	android:theme
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:13-54
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:13-66
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:65:13-69:29
action#android.intent.action.MAIN
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:17-69
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:25-66
category#android.intent.category.LAUNCHER
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:17-77
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:27-74
meta-data#unityplayer.UnityActivity
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:13-73:40
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:17-37
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:17-57
meta-data#android.notch_support
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:74:13-76:40
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:17-37
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:75:17-53
meta-data#unity.splash-mode
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:9-81:33
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:13-30
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:13-45
meta-data#unity.splash-enable
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:9-84:36
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:84:13-33
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:13-47
meta-data#unity.allow-resizable-window
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:85:9-87:37
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:13-34
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:13-56
meta-data#notch.config
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:9-90:50
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:13-47
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:13-40
meta-data#TEST_DEVICE_ID
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:9-93:64
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:13-61
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:13-42
meta-data#unity.build-id
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:9-96:68
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:13-65
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:13-42
meta-data#UMENG_APPKEY
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:9-99:56
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:13-53
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:13-40
meta-data#UMENG_CHANNEL
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:100:9-102:38
	android:value
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:13-35
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:13-41
provider#androidx.core.content.FileProvider
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:9-112:20
	android:grantUriPermissions
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:13-47
	android:authorities
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:13-64
	android:exported
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:13-37
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:13-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:109:13-111:54
	android:resource
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:111:17-51
	android:name
		ADDED from [:unityLibrary] E:\huoli-rk3566\hl071302\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:110:17-67
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:5-79
	android:name
		ADDED from [com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:22-76
service#com.efs.sdk.memleaksdk.monitor.UMonitorService
ADDED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:10:9-14:41
	android:process
		ADDED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:14:13-38
	android:enabled
		ADDED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:12:13-35
	android:exported
		ADDED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:11:13-74
