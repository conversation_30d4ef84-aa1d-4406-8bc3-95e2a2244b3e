package com.unity3d.player;

import android.content.Context;
import android.util.Log;

import java.io.IOException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * author : admin
 * date : 2025/7/3 10:21
 * description :
 */
public class NetRequest {
    private final String VERSION_UPDATE = "https://sport.eaglecreative.top/v1/index/version";
    private onRequestListener loginResultListener;
    private Context context;

    public NetRequest(Context context) {
        this.context = context;
    }

    public void setNetworkRequest(onRequestListener loginResultListener) {
        this.loginResultListener = loginResultListener;
    }

    public void updateVersion() {
        OkHttpClient okHttpClient = new OkHttpClient();
        String drivers = "android";
        Request request = new Request.Builder().url(VERSION_UPDATE)
                .addHeader("drivers", drivers)
                .addHeader("oaid", "")
                .addHeader("androidid", "")
                .build();
        Call call = okHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.i("TAG===", "updateVersiononFailure");
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String string = response.body().string();
                Log.i("TAG===", string);
                loginResultListener.getRequestMsg(1, string);
            }
        });
    }

    interface onRequestListener {
        void getRequestMsg(int type, String msg);
    }

}
